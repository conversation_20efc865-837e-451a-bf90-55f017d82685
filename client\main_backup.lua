-- Backup client script for clothing store
-- This is a working version if the encrypted files don't work

local ESX = nil
local isInClothingShop = false
local currentShop = nil

print("^2[CLOTHING STORE] Script wird geladen...^0")

-- Initialize ESX
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(100)
    end
    print("^2[CLOTHING STORE] ESX geladen!^0")
end)

-- Create blips for clothing stores
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- Warte bis alles geladen ist
    print("^2[CLOTHING STORE] Erstelle Blips für " .. #Config.stores .. " Läden^0")

    for i, coords in ipairs(Config.stores) do
        local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
        SetBlipSprite(blip, 73)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 47)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(Config.blipName)
        EndTextCommandSetBlipName(blip)
        print("^2[CLOTHING STORE] Blip erstellt bei: " .. coords.x .. ", " .. coords.y .. ", " .. coords.z .. "^0")
    end
    print("^2[CLOTHING STORE] Alle Blips erstellt!^0")
end)

-- Main thread for markers and interaction
Citizen.CreateThread(function()
    print("^2[CLOTHING STORE] Marker-Thread gestartet^0")

    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local isNearShop = false

        for i, coords in ipairs(Config.stores) do
            local distance = #(playerCoords - coords)

            if distance < 15.0 then
                isNearShop = true

                -- Draw marker
                DrawMarker(1, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 1.0, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)

                if distance < 3.0 then
                    -- Show help text
                    SetTextComponentFormat("STRING")
                    AddTextComponentString("Drücke ~INPUT_CONTEXT~ um den Kleidungsladen zu öffnen")
                    DisplayHelpTextFromStringLabel(0, 0, 1, -1)

                    -- Check for key press
                    if IsControlJustReleased(0, 38) then -- E key
                        print("^2[CLOTHING STORE] E-Taste gedrückt, öffne Shop^0")
                        OpenClothingShop(i)
                    end
                end
            end
        end

        if not isNearShop then
            Citizen.Wait(500)
        end
    end
end)

-- Open clothing shop
function OpenClothingShop(shopIndex)
    if isInClothingShop then
        return
    end
    
    isInClothingShop = true
    currentShop = shopIndex
    
    -- Set NUI focus
    SetNuiFocus(true, true)
    
    -- Send data to NUI
    SendNUIMessage({
        type = "openShop",
        parts = Config.parts
    })
    
    -- Start camera
    TriggerEvent('final_clothingstore:startCamera')
end

-- Close clothing shop
function CloseClothingShop()
    if not isInClothingShop then
        return
    end
    
    isInClothingShop = false
    currentShop = nil
    
    -- Remove NUI focus
    SetNuiFocus(false, false)
    
    -- Send close message to NUI
    SendNUIMessage({
        type = "closeShop"
    })
    
    -- Stop camera
    TriggerEvent('final_clothingstore:stopCamera')
end

-- NUI Callbacks
RegisterNUICallback('closeShop', function(data, cb)
    CloseClothingShop()
    cb('ok')
end)

RegisterNUICallback('pay', function(data, cb)
    local method = data.method
    local amount = data.amount or 0
    
    TriggerServerEvent('final_clothingstore:pay', method, amount)
    cb('ok')
end)

RegisterNUICallback('saveOutfit', function(data, cb)
    local outfitName = data.name
    local outfitData = data.outfit
    
    TriggerServerEvent('final_clothingstore:saveOutfit', outfitName, outfitData)
    cb('ok')
end)

-- Server event handlers
RegisterNetEvent('final_clothingstore:paymentSuccess')
AddEventHandler('final_clothingstore:paymentSuccess', function()
    SendNUIMessage({
        type = "paymentSuccess"
    })
end)

RegisterNetEvent('final_clothingstore:paymentFailed')
AddEventHandler('final_clothingstore:paymentFailed', function(message)
    SendNUIMessage({
        type = "paymentFailed",
        message = message
    })
end)

-- Export functions
exports('isInClothingShop', function()
    return isInClothingShop
end)

exports('closeShop', function()
    CloseClothingShop()
end)
