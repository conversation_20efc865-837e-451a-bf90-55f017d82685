-- <PERSON><PERSON> einfache Test-Version für Kleidungsladen
print("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>DEN TEST SCRIPT WIRD GELADEN!")

-- Test-Koordinaten (Strawberry Kleidungsladen)
local testCoords = vector3(72.3155, -1399.0750, 29.3761)

-- Sofortiger Test beim <PERSON>reateThread(function()
    print("THREAD GESTARTET!")
    TriggerEvent('chat:addMessage', {
        color = { 255, 0, 0},
        multiline = true,
        args = {"[KLEIDUNGSLADEN]", "Script wurde geladen!"}
    })
end)

-- <PERSON><PERSON><PERSON> einen Test-Blip
CreateThread(function()
    Wait(3000)
    print("ERSTELLE BLIP...")

    local blip = AddBlipForCoord(testCoords.x, testCoords.y, testCoords.z)
    SetBlipSprite(blip, 73)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.2)
    SetBlipColour(blip, 2) -- <PERSON><PERSON><PERSON><PERSON>
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("TEST KLEIDUNG")
    EndTextCommandSetBlipName(blip)

    print("BLIP ERSTELLT!")
    TriggerEvent('chat:addMessage', {
        color = { 0, 255, 0},
        multiline = true,
        args = {"[KLEIDUNGSLADEN]", "Blip erstellt! Gehe zu Strawberry Kleidungsladen!"}
    })
end)

-- Test-Marker und Interaktion
CreateThread(function()
    print("MARKER THREAD GESTARTET!")

    while true do
        Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - testCoords)

        if distance < 25.0 then
            -- Zeichne SEHR großen roten Marker
            DrawMarker(1, testCoords.x, testCoords.y, testCoords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.0, 5.0, 3.0, 255, 0, 0, 200, false, true, 2, false, nil, nil, false)

            if distance < 8.0 then
                -- Zeige Text
                SetTextComponentFormat("STRING")
                AddTextComponentString("~r~TEST KLEIDUNGSLADEN~w~\nDrücke ~INPUT_CONTEXT~ zum Testen")
                DisplayHelpTextFromStringLabel(0, 0, 1, -1)

                if IsControlJustReleased(0, 38) then -- E key
                    print("E-TASTE GEDRÜCKT!")

                    TriggerEvent('chat:addMessage', {
                        color = { 0, 255, 0},
                        multiline = true,
                        args = {"[SUCCESS]", "E-Taste funktioniert! Kleidungsladen Script läuft!"}
                    })
                end
            end
        else
            Wait(500)
        end
    end
end)

print("TEST SCRIPT VOLLSTÄNDIG GELADEN!")
