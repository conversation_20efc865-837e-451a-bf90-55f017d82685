-- <PERSON><PERSON> einfache Test-Version für Kleidungsladen
print("^1[TEST] Kleidungsladen Test-Script wird geladen...^0")

-- Test-Koordinaten (Strawberry Kleidungsladen)
local testCoords = vector3(72.3155, -1399.0750, 29.3761)

-- Erst<PERSON> einen Test-Blip
Citizen.CreateThread(function()
    Citizen.Wait(2000)
    print("^1[TEST] Erstelle Test-Blip...^0")
    
    local blip = AddBlipForCoord(testCoords.x, testCoords.y, testCoords.z)
    SetBlipSprite(blip, 73)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.0)
    SetBlipColour(blip, 2) -- <PERSON><PERSON><PERSON>n
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("TEST Kleidungsladen")
    EndTextCommandSetBlipName(blip)
    
    print("^1[TEST] Test-Blip erstellt bei: " .. testCoords.x .. ", " .. testCoords.y .. ", " .. testCoords.z .. "^0")
end)

-- Test-Marker und Interaktion
Citizen.CreateThread(function()
    print("^1[TEST] Test-Marker Thread gestartet^0")
    
    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - testCoords)
        
        if distance < 20.0 then
            -- Zeichne großen roten Marker
            DrawMarker(1, testCoords.x, testCoords.y, testCoords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 2.0, 255, 0, 0, 150, false, true, 2, false, nil, nil, false)
            
            if distance < 5.0 then
                -- Zeige Text
                SetTextComponentFormat("STRING")
                AddTextComponentString("~r~TEST KLEIDUNGSLADEN~w~\nDrücke ~INPUT_CONTEXT~ zum Testen")
                DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                
                if IsControlJustReleased(0, 38) then -- E key
                    print("^1[TEST] E-Taste gedrückt! Test erfolgreich!^0")
                    
                    -- Zeige Notification
                    SetNotificationTextEntry("STRING")
                    AddTextComponentString("~g~TEST ERFOLGREICH!~w~\nKleidungsladen funktioniert!")
                    DrawNotification(false, false)
                    
                    -- Öffne Test-UI
                    SetNuiFocus(true, true)
                    SendNUIMessage({
                        type = "openShop",
                        parts = Config.parts
                    })
                end
            end
        else
            Citizen.Wait(1000)
        end
    end
end)

-- NUI Test-Callback
RegisterNUICallback('closeShop', function(data, cb)
    print("^1[TEST] NUI Close-Callback empfangen^0")
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = "closeShop"
    })
    cb('ok')
end)

print("^1[TEST] Test-Script vollständig geladen!^0")
