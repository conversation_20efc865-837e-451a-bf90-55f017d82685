Config = {
    -- list of clothing parts that will appear in store UI and their component name (from 'skinchanger' script)
    parts = {
        -- script by default will add _1 and _2 suffix to 'comp' if it's provided
        -- if you want to manually provide drawable and texture index, then omit the 'comp' field
        -- and instead set 'drawable' and 'texture' fields (check arms for example)
        { label = "Helm", comp = "helmet", icon = "hat.png", price = 50 },
        { label = "Brille", comp = "glasses", icon = "glasses.png", price = 50 },
        { label = "Uhren", comp = "watches", icon = "watches.png", price = 50 },
        { label = "Hose", comp = "pants", icon = "pants.png", price = 50 },
        { label = "Schuhe", comp = "shoes", icon = "shoes.png", price = 50 },
        { label = "Kette", comp = "chain", icon = "chain.png", price = 50 },
        { label = "Rucksack", comp = "bags", icon = "bag.png", price = 50 },
        { label = "Torso", comp = "torso", icon = "torso.png", price = 50 },
        { label = "TShirt", comp = "tshirt", icon = "tshirt.png", price = 50 },
        { label = "Arme", drawable = "arms", texture = "arms_2", icon = "arms.png", price = 50 },
        { label = "Maske", comp = "mask", icon = "mask.png", price = 50 },
        { label = "Weste", comp = "bproof", icon = "bproof.png", price = 50 },
    },

    blipName = "Kleidungsladen",
    -- list of coords where store markers are
    stores = {
        vector3(72.3155, -1399.0750, 29.3761),
        vector3(-276.8420, 6640.4771, 10.8658),
        vector3(-703.6830, -152.2655, 37.4152),
        vector3(-167.9820, -299.0144, 39.7333),
        vector3(428.7336, -800.0488, 29.4911),
        vector3(-829.3900, -1073.7886, 11.3281),
        vector3(-1447.7078, -242.5320, 49.8211),
        vector3(11.4377, 6514.3179, 31.8778),
        vector3(123.5264, -219.4038, 54.5577),
        vector3(1696.0116, 4829.0557, 42.0631),
        vector3(618.0446, 2759.6646, 42.0882),
        vector3(1190.7197, 2713.3220, 38.2226),
        vector3(-1193.3296, -772.3154, 17.3247),
        vector3(-3172.5139, 1048.1122, 20.8632),
        vector3(-1108.0610, 2708.7378, 19.1078),
        vector3(-808.6682, -2346.8279, 14.8400),
        vector3(-1039.4954, -2780.2529, 21.3432),
        vector3(-332.4688, 7209.4702, 6.7981),
    },
}

function Notify(msg, severity)
    print(severity, msg)
end