# Kleidungsladen Fehlerbehebung

## Problem: <PERSON><PERSON>, <PERSON><PERSON><PERSON> und UI werden nicht angezeigt

### Ursachen und Lösungen:

1. **Verschlüsselte Client-Dateien**
   - Die ursprünglichen Client-Dateien (main.lua, camera.lua, nui.lua) sind verschlüsselt/obfuskiert
   - **Lösung**: Backup-<PERSON>ien wurden erstellt und im fxmanifest.lua aktiviert

2. **Fehlende Server-Dateien**
   - Das fxmanifest.lua verwies auf nicht existierende Server-Dateien
   - **Lösung**: server/main.lua wurde erstellt mit grundlegenden Funktionen

3. **Dependency-Probleme**
   - Das Script benötigt "skinchanger" als Dependency
   - **Lösung**: <PERSON><PERSON> sicher, dass skinchanger installiert und gestartet ist

### Installationsschritte:

1. **<PERSON><PERSON> sicher, dass ESX läuft**
   ```
   ensure es_extended
   ```

2. **<PERSON><PERSON> sic<PERSON>, dass skinchanger läuft**
   ```
   ensure skinchanger
   ```

3. **Starte den Kleidungsladen**
   ```
   ensure final_clothingstore
   ```

### Wenn das Problem weiterhin besteht:

1. **Überprüfe die Server-Konsole** auf Fehlermeldungen
2. **Überprüfe die Client-Konsole** (F8) auf JavaScript-Fehler
3. **Stelle sicher, dass alle Dependencies installiert sind**:
   - ESX
   - skinchanger

### Funktionen der Backup-Dateien:

- **client/main_backup.lua**: Hauptlogik für Marker, Blips und Interaktion
- **client/camera_backup.lua**: Kamera-System für den Kleidungsladen
- **client/nui_backup.lua**: NUI-Callbacks und Kleidungs-Updates
- **server/main.lua**: Server-seitige Zahlungsabwicklung

### Koordinaten der Kleidungsläden:

Die Läden befinden sich an folgenden Koordinaten (aus config.lua):
- Strawberry: 72.32, -1399.08, 29.38
- Paleto Bay: -276.84, 6640.48, 10.87
- Rockford Hills: -703.68, -152.27, 37.42
- Und weitere...

### Debugging:

Wenn die Marker immer noch nicht angezeigt werden:
1. Gehe zu einer der Koordinaten
2. Öffne die Client-Konsole (F8)
3. Schaue nach Fehlermeldungen
4. Überprüfe, ob ESX geladen ist: `TriggerEvent('esx:getSharedObject', function(obj) print(obj) end)`
