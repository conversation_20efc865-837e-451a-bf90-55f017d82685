-- Backup camera script for clothing store

local cam = nil
local isCamActive = false
local heading = 0.0

-- Start camera
RegisterNetEvent('final_clothingstore:startCamera')
AddEventHandler('final_clothingstore:startCamera', function()
    if isCamActive then
        return
    end
    
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    -- Create camera
    cam = CreateCamWithParams("DEFAULT_SCRIPTED_CAMERA", coords.x + 2.0, coords.y + 2.0, coords.z + 1.0, 0.0, 0.0, 0.0, 50.0, false, 0)
    
    -- Point camera at player
    PointCamAtEntity(cam, playerPed, 0.0, 0.0, 0.0, true)
    
    -- Set camera active
    SetCamActive(cam, true)
    RenderScriptCams(true, true, 1000, true, true)
    
    isCamActive = true
    
    -- Start camera control thread
    Citizen.CreateThread(function()
        while isCamActive do
            Citizen.Wait(0)
            
            -- Disable player movement
            DisableControlAction(0, 30, true) -- A/D
            DisableControlAction(0, 31, true) -- W/S
            DisableControlAction(0, 32, true) -- W
            DisableControlAction(0, 33, true) -- S
            DisableControlAction(0, 34, true) -- A
            DisableControlAction(0, 35, true) -- D
            
            -- Camera rotation with mouse
            local mouseX = GetDisabledControlNormal(0, 1) * 2.0
            local mouseY = GetDisabledControlNormal(0, 2) * 2.0
            
            heading = heading - mouseX
            
            if heading > 360.0 then
                heading = 0.0
            elseif heading < 0.0 then
                heading = 360.0
            end
            
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            local radians = math.rad(heading)
            
            local camX = coords.x + (math.cos(radians) * 3.0)
            local camY = coords.y + (math.sin(radians) * 3.0)
            local camZ = coords.z + 1.0
            
            SetCamCoord(cam, camX, camY, camZ)
            PointCamAtEntity(cam, playerPed, 0.0, 0.0, 0.0, true)
        end
    end)
end)

-- Stop camera
RegisterNetEvent('final_clothingstore:stopCamera')
AddEventHandler('final_clothingstore:stopCamera', function()
    if not isCamActive then
        return
    end
    
    isCamActive = false
    
    -- Destroy camera
    if cam then
        SetCamActive(cam, false)
        RenderScriptCams(false, true, 1000, true, true)
        DestroyCam(cam, false)
        cam = nil
    end
    
    heading = 0.0
end)
