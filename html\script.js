const closeButton = document.querySelector(".main__fasocity-header-container-right-close-container");
const cancelOutfit = document.querySelector("#cancelOutfit");
const saveOutfit = document.querySelector("#saveOutfit");
const handsUp = document.querySelector("#handsUpButton");

const clothingContainer = ".main__clothing-opacity-container";
const partsContainer = ".main__clothing-scroll-container";
const outfitPopup = ".main__clothing-grid-item-right-popup";
const outfitsContainer = ".main__clothing-safe-outfit-container";
const sharedOutfitsContainer = ".sharedOutfitsHere";

const drawableInput = document.querySelector("#drawableValue");
const drawableLeft = document.querySelector("#drawableLeft");
const drawableRight = document.querySelector("#drawableRight");

const textureInput = document.querySelector("#textureValue");
const textureLeft = document.querySelector("#textureLeft");
const textureRight = document.querySelector("#textureRight");

const outfitNameInput = document.querySelector("#outfitNameInput");

const cashPayment = document.querySelector("#cashPayment");
const cardPayment = document.querySelector("#cardPayment");

const searchOutfitInput = document.querySelector("#searchOutfitsInput");
const addOutfitInput = document.querySelector("#addOutfitInput");

let currentPrice = 0;
let drawable = {
    name: "torso_1",
    min: 0,
    max: 300,
};
let texture = {
    name: "torso_2",
    min: 0,
    max: 300,
};

function post(endpoint, body) {
    return $.post(
        `https://${GetParentResourceName()}/${endpoint}`,
        JSON.stringify(body),
    );
}

async function pay(method) {
    const paid = await post("pay", { method }); 
    if (!paid) {
        return;
    }

    $(outfitPopup).css("display", "block");
    $(clothingContainer).css("display", "none");
}

cashPayment.addEventListener("click", () => pay("money"));
cardPayment.addEventListener("click", () => pay("bank"));

const sanitizeInput = (input) => input.replace(/[^a-zA-Z0-9äöüÄÖÜß ]/g, ""); 

saveOutfit.addEventListener("click", () => {
    const name = sanitizeInput(outfitNameInput.value);
    post("saveOutfit", { name });
    outfitNameInput.value = "";
    closeUi();
});

cancelOutfit.addEventListener("click", () => closeUi());

function validateInput(input, minValue, maxValue) {
    if (maxValue < minValue) { // fix for some weird values returned from skinchanger where max value is -1
        maxValue = minValue;
    }

    let value = input.value;

    value = value.replace(/[^0-9-]/g, ""); // remove all chars except digits and minus sign
    value = value.replace(/(?!^)-/g, ""); // remove any '-' that's not at the beginning

    if (value.length > 3) {
        value = value.slice(0, 3);
    }

    if (value > maxValue) {
        value = maxValue;
    } else if (value < minValue) {
        value = minValue;
    }

    input.value = value;
}

const increaseValue = (input) => {
    input.value = Number(input.value) + 1;
    input.dispatchEvent(new Event("input", { bubbles: true }));
}

const decreaseValue = (input) => {
    input.value = Number(input.value) - 1;
    input.dispatchEvent(new Event("input", { bubbles: true }));
}

drawableInput.addEventListener("input", async () => {
    validateInput(drawableInput, drawable.min, drawable.max);

    // reset texture value
    if (Number(textureInput.value) !== 0) {
        textureInput.value = 0;
        textureInput.dispatchEvent(new Event("input", { bubbles: true }));
    }

    // update drawable
    if (drawableInput.value !== "-" && !isNaN(Number(drawableInput.value))) {
        const res = await post("updateDrawable", {
            componentName: drawable.name,
            textureName: texture.name,
            value: drawableInput.value,
            price: currentPrice,
        });
        texture.min = res.texture.min;
        texture.max = res.texture.max;
        $("#clothprice").text(`${res.totalPrice} €`);
    }
});
drawableLeft.addEventListener("click", () => decreaseValue(drawableInput));
drawableRight.addEventListener("click", () => increaseValue(drawableInput));

textureInput.addEventListener("input", () => {
    validateInput(textureInput, texture.min, texture.max);
    post("updateTexture", {
        componentName: texture.name,
        value: textureInput.value,
    })
});
textureLeft.addEventListener("click", () => decreaseValue(textureInput));
textureRight.addEventListener("click", () => increaseValue(textureInput));

handsUp.addEventListener("click", () => post("handsUp"));

function insertPart(part) {
    const component = `
    <div class="main__clothing-scroll-item">
        <div class="main__clothing-scroll-item-img">
            <img src="images/parts/${part.icon}" alt="${part.label} icon">
        </div>
        <div class="main__clothing-scroll-item-name">
            <p>${part.label}</p>
        </div>
    </div>
    `;

    return $(component)
    .appendTo(partsContainer)
    .on("click", async () => {
        currentPrice = part.price;

        drawable.name = part.comp !== undefined ? `${part.comp}_1` : part.drawable;
        texture.name = part.comp !== undefined ? `${part.comp}_2` : part.texture;

        // fetch max drawable and texture for this comp
        const partData = await post("getPartData", {
            drawableName: drawable.name,
            textureName: texture.name,
        });
        drawable.min = partData.drawable.min;
        drawable.max = partData.drawable.max;

        texture.min = partData.texture.min;
        texture.max = partData.texture.max;

        drawableInput.value = partData.drawable.value;
        textureInput.value = partData.texture.value;
    });
}

function insertOutfit({ id, name, skinData, code }) {
    const isShared = code !== undefined;

    const component = `
    <div class="main__clothing-outfit-item">
        <div class="main__clothing-outfit-item-left">
            <div class="main__clothing-outfit-item-icon">
                <img src="images/torso.png" alt="" />
            </div>
            <div class="main__clothing-outfit-item-name">
                <p>${isShared ? code : ""}</p>
                <p>${name}</p>
            </div>
            <div class="main__clothing-outfit-btn-container">
                <div class="main__clothing-outfit-btn-2">
                    <i class="fa-regular fa-xmark"></i>
                </div>
            </div>
        </div>
        <div class="main__corleone-outfit-item-right ${isShared && "disabled"}">
            <i class="fa-solid fa-share"></i>
        </div>
    </div>
    `;

    const outfit = $(component).appendTo(outfitsContainer);

    outfit
    .find(".main__clothing-outfit-btn-2")
    .on("click", function() {
        post("deleteOutfit", { id });
        outfit.remove();
    });

    if (!isShared) {
        outfit
        .find(".main__corleone-outfit-item-right")
        .on("click", async function() {
            await post("shareOufit", { id });
            $(this).addClass("disabled");
            $(this).off("click");
        });
    }

    outfit
    .find(".main__clothing-outfit-item-name")
    .on("click", function() {
        post("equipOutfit", { skinData });
    });
}

function insertSharedOutfit({ name, code, skinData }) {
    const component = `
    <div class="main__clothing-outfit-item-left" style="margin-bottom: 1vh">
        <div class="main__clothing-outfit-item-icon">
            <img src="images/parts/torso.png" alt="">
        </div>
        <div class="main__clothing-outfit-item-name-2">
            <p class="outfit__name-2">${name}</p>
            <div class="main__clothing-outfit-item-share-container">
                <div class="main__coroelone-clothing-outfit-item-share-owner">
                    <p>Geteilt</p>
                </div>
                <div class="main__clothing-outfit-item-share-name">
                    <p>${code}</p>
                </div>
            </div>
        </div>
    </div>
    `;

    $(component)
    .appendTo(".main__clothing-outfit-container")
    .children(".main__clothing-outfit-item-name-2")
    .on("click", () => {
        post("equipOutfit", { skinData });
    });
}

const outfitsGrid = ".main__clothing-outfit-gird-container";
$("#clothingCategory").on("click", function() {
    $(".main__clothing-category-btn").removeClass("active");
    $(this).addClass("active");

    $(clothingContainer).css("display", "block");
    $(outfitsGrid).css("display", "none");
    $(sharedOutfitsContainer).css("display", "none");
});

$("#outfitsCategory").on("click", function() {
    $(".main__clothing-category-btn").removeClass("active");
    $(this).addClass("active");

    $(clothingContainer).css("display", "none");
    $(outfitsGrid).css("display", "block");
    $(sharedOutfitsContainer).css("display", "none");
});

$("#sharedCategory").on("click", function() {
    $(".main__clothing-category-btn").removeClass("active");
    $(this).addClass("active");

    $(clothingContainer).css("display", "none");
    $(outfitsGrid).css("display", "none");
    $(sharedOutfitsContainer).css("display", "block");
});

let outfits = [];

searchOutfitInput.addEventListener("input", () => {
    const filter = searchOutfitInput.value;
    const newOutfits = outfits.filter(o => o.name.startsWith(filter));

    $(outfitsContainer).empty();
    newOutfits.forEach(outfit => insertOutfit(outfit));
});

addOutfitInput.addEventListener("keydown", async (e) => {
    if (e.key === "Enter") {
        const outfit = await post("addSharedOutfit", { code: addOutfitInput.value });
        if (outfit !== null) {
            insertSharedOutfit(outfit);
            addOutfitInput.value = "";
        }
    }
});

window.addEventListener("message", ({ data }) => {
    if (data.action === "open") {
        $(partsContainer).empty();
        data.parts.forEach((part, index) => {
            const component = insertPart(part);

            if (index === 0) {
                component.trigger("click");
            }
        });

        $(".main__clothing-category-btn").removeClass("active");
        $("#clothingCategory").addClass("active");
        $(outfitPopup).css("display", "none");
        $(outfitsContainer).empty();
        $(".main__clothing-outfit-container").empty();
        $(clothingContainer).css("display", "block");
        $("body").fadeIn();
    } else if (data.action === "close") {
        closeUi();
    } else if (data.action === "outfits") {
        outfits = data.outfits;
        outfits.forEach(outfit => insertOutfit(outfit));
    } else if (data.action === "sharedOutfits") {
        data.outfits.forEach(outfit => insertSharedOutfit(outfit));
    }
});


// CLOSING LOGIC
function closeUi() {
    $("body").fadeOut();
    post("disableFocus");
}

window.addEventListener("keydown", ({ key }) => {
    if (key === "Escape") {
        closeUi();
    }
});

closeButton.addEventListener("click", closeUi);