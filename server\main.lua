-- Basic server-side script for clothing store
-- This handles payment processing and outfit saving

-- Register server events
RegisterServerEvent('final_clothingstore:pay')
AddEventHandler('final_clothingstore:pay', function(method, amount)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    
    if not xPlayer then
        return
    end
    
    if method == 'cash' then
        if xPlayer.getMoney() >= amount then
            xPlayer.removeMoney(amount)
            TriggerClientEvent('final_clothingstore:paymentSuccess', src)
        else
            TriggerClientEvent('final_clothingstore:paymentFailed', src, 'Nicht genug Bargeld!')
        end
    elseif method == 'card' then
        if xPlayer.getAccount('bank').money >= amount then
            xPlayer.removeAccountMoney('bank', amount)
            TriggerClientEvent('final_clothingstore:paymentSuccess', src)
        else
            TriggerClientEvent('final_clothingstore:paymentFailed', src, 'Nicht genug Geld auf der Bank!')
        end
    end
end)

-- Save outfit
RegisterServerEvent('final_clothingstore:saveOutfit')
AddEventHandler('final_clothingstore:saveOutfit', function(outfitName, outfitData)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    
    if not xPlayer then
        return
    end
    
    -- Here you would save to database
    -- For now, just trigger success
    TriggerClientEvent('final_clothingstore:outfitSaved', src)
end)

-- Get player outfits
RegisterServerEvent('final_clothingstore:getOutfits')
AddEventHandler('final_clothingstore:getOutfits', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    
    if not xPlayer then
        return
    end
    
    -- Here you would load from database
    -- For now, return empty array
    TriggerClientEvent('final_clothingstore:receiveOutfits', src, {})
end)
