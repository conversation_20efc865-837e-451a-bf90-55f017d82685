-- Backup NUI script for clothing store

local currentClothing = {}
local originalClothing = {}

-- Initialize clothing system
Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(100)
    end
    
    -- Get original clothing
    TriggerEvent('skinchanger:getSkin', function(skin)
        originalClothing = skin
        currentClothing = skin
    end)
end)

-- NUI Callbacks
RegisterNUICallback('updateClothing', function(data, cb)
    local part = data.part
    local drawable = data.drawable
    local texture = data.texture
    
    if part and drawable and texture then
        -- Update current clothing
        if part.comp then
            currentClothing[part.comp .. '_1'] = drawable
            currentClothing[part.comp .. '_2'] = texture
        elseif part.drawable and part.texture then
            currentClothing[part.drawable] = drawable
            currentClothing[part.texture] = texture
        end
        
        -- Apply clothing
        TriggerEvent('skinchanger:loadSkin', currentClothing)
    end
    
    cb('ok')
end)

RegisterNUICallback('resetClothing', function(data, cb)
    -- Reset to original clothing
    currentClothing = originalClothing
    TriggerEvent('skinchanger:loadSkin', originalClothing)
    cb('ok')
end)

RegisterNUICallback('getClothingLimits', function(data, cb)
    local part = data.part
    local limits = {
        drawable = { min = 0, max = 100 },
        texture = { min = 0, max = 10 }
    }
    
    if part and part.comp then
        -- Get actual limits from game
        local playerPed = PlayerPedId()
        local componentId = GetComponentId(part.comp)
        
        if componentId then
            limits.drawable.max = GetNumberOfPedDrawableVariations(playerPed, componentId) - 1
            limits.texture.max = GetNumberOfPedTextureVariations(playerPed, componentId, 0) - 1
        end
    end
    
    cb(limits)
end)

RegisterNUICallback('handsUp', function(data, cb)
    local playerPed = PlayerPedId()
    
    if IsEntityPlayingAnim(playerPed, 'missminuteman_1ig_2', 'handsup_base', 3) then
        ClearPedSecondaryTask(playerPed)
    else
        RequestAnimDict('missminuteman_1ig_2')
        while not HasAnimDictLoaded('missminuteman_1ig_2') do
            Citizen.Wait(100)
        end
        TaskPlayAnim(playerPed, 'missminuteman_1ig_2', 'handsup_base', 8.0, -8, -1, 49, 0, 0, 0, 0)
    end
    
    cb('ok')
end)

-- Helper function to get component ID from name
function GetComponentId(compName)
    local components = {
        mask = 1,
        torso = 11,
        pants = 4,
        bags = 5,
        shoes = 6,
        chain = 7,
        tshirt = 8,
        bproof = 9,
        helmet = 0,
        glasses = 1,
        watches = 6
    }
    
    return components[compName]
end
