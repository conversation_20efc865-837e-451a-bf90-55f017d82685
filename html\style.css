@import url("https://fonts.cdnfonts.com/css/svn-gilroy?styles=55332,55331");
@import url("https://fonts.cdnfonts.com/css/bebas-neue");

:root {
  --ff-header: "SVN-Gil<PERSON>", sans-serif;
  --ff-bebas: "Bebas Neue", sans-serif;
  --ff-body: "Roboto", sans-serif;
  --color-white: rgba(255, 255, 255, 1);
  --border-radius-close: 0.5vh;
}

*,
*::before,
*::after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--ff-header);
  user-select: none;
  color: var(--color-white);

  display: none;
}

input {
  border: none;
  outline: none;
  background-color: transparent;
}

.main__clothing-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: url(images/bg.png);
  background-size: cover;
  background-position: center top left;
  z-index: -1;
}

.main__fasocity-header-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main__fasocity-header-left-stripe {
  width: 0.1vh;
  height: 3.5vh;
  background: rgba(255, 255, 255, 0.5);
  margin-right: 0.5vh;
}

.main__corleone-outfit-item-right.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.main__fasocity-header-left {
  font-family: var(--ff-arame);
  font-size: 1.5vh;
}

.main__fasocity-header-left p:first-child {
  font-weight: 100;
  color: #ffffff80;
  letter-spacing: 0.5vh;
  line-height: 1.5vh;
}

.main__fasocity-header-left P:last-child {
  font-weight: 700;
  font-size: 2.4vh;
  color: #fff;
  line-height: 2.6vh;
}

.main__fasocity-header-container-right {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.main__fasocity-header-container-right-close-container {
  width: 3.5vh;
  height: 3.5vh;
  border-radius: var(--border-radius-close);
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(184, 129, 28, 0) 100%
  );
  box-shadow: 0 0 3.7vh #ffffff26 inset;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-white);
  font-size: 1.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__fasocity-header-container-right-close-container i {
  transition: 0.2s ease-in;
}

.main__fasocity-header-container-right-close-container:hover {
  box-shadow: 0 0 1.7vh #ffffff80 inset;
}

.main__fasocity-header-container-right-close-container:hover i {
  transform: rotateY(180deg);
}

.main__fasocity-header-bg {
  position: absolute;
  top: 1vh;
  left: 3vh;
}

.main__fasocity-header-bg img {
  width: 6vh;
}

.main__clothing-container {
  width: 32.5vh;
  height: 100vh;
  padding: 2vh;
}

.main__clothing-grid-item-header {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--ff-bebas);
  padding-bottom: 1vh;
}

.main__clothing-grid-item-header-left {
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  font-size: 3vh;
  font-weight: 300;
}

.main__clothing-grid-item-header-left p span {
  font-weight: 500;
  color: #fff;
}

.main__clothing-grid-item-header-right-btn {
  width: 3.25vh;
  height: 3.25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to top, #ff9d09, #be3b42);
  color: #fff;
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: 0.2s ease-in;
  font-size: 1.5vh;
}

.main__clothing-grid-item-header-right-btn i {
  transition: 0.2s ease-in;
}

.main__clothing-grid-item-header-right-btn:hover {
  box-shadow: 0vh 0vh 1.7vh #be3b42;
}

.main__clothing-grid-item-header-right-btn:hover i {
  transform: rotate(90deg);
}

.main__clothing-grid-item-header-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0.1vh;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0)
  );
}

.main__clothing-grid-item-header-border-highlight {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 17.5vh;
  height: 0.1vh;
  background: #fd9103;
  box-shadow: 0vh 0vh 1.7vh #fd7403;
}

.main__clothing-category-btn-container {
  width: 100%;
  margin-top: 1.5vh;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1vh;
}

.main__clothing-category-btn {
  position: relative;
  padding: 1vh;
  border-radius: 0.3vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
  font-size: 1.25vh;
  color: #fff;
  text-align: center;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-category-btn:hover,
.main__clothing-category-btn.active {
  box-shadow: 0vh 0vh 3vh #382603 inset, 0vh 0vh 1.7vh #c17717;
  background: #9c6614;
  border: 0.1vh solid transparent;
}

.main__clothing-category-search {
  margin-top: 1vh;
  margin-bottom: 1vh;
  width: 100%;
  padding: 1vh;
  display: flex;
  align-items: center;
  gap: 1.5vh;
  border-radius: 0.3vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  font-size: 1.5vh;
  color: #fff;
}

.main__clothing-category-search input {
  font-family: var(--ff-body);
  color: #fff;
  width: 100%;
  /* padding-bottom: 0.25vh; */
  /* border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05); */
  outline: none;
  border: none;
  background: none;
  margin-top: 2px;
}

.main__clothing-category-search input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.main__clothing-scroll-container {
  width: 100%;
  height: 51vh;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1vh;
  padding-right: 1vh;
  margin-top: 1vh;
  overflow-y: scroll;
}

.main__clothing-scroll-container::-webkit-scrollbar {
  width: 0.3vh;
  border-radius: 5vh;
}

.main__clothing-scroll-container::-webkit-scrollbar-track {
  width: 0.3vh;
  border-radius: 5vh;
  background: rgba(255, 255, 255, 0.05);
}

.main__clothing-scroll-container::-webkit-scrollbar-thumb {
  width: 0.3vh;
  border-radius: 5vh;
  background: #c17717;
}

.main__clothing-scroll-item {
  width: 100%;
  height: 12vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-scroll-item:hover {
  background-size: 150%;
  background-position: center;
}

.main__clothing-scroll-item-name {
  font-family: var(--ff-bebas);
  font-size: 2vh;
  color: #fff;
  margin-top: 1vh;
}

.main__clothing-scroll-item-img {
  width: 7.5vh;
  height: 7vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main__clothing-scroll-item-img img {
  width: 100%;
}

.main__clothing-input-item-wrapper {
  margin-top: 1vh;
  width: 100%;
  padding: 0.5vh;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
}

.main__clothing-input-item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main__clothing-input-item-btn {
  width: 3vh;
  height: 3vh;
  border-radius: 0.25vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
  color: #fff;
}

.main__clothing-input-item-btn:hover {
  box-shadow: 0vh 0vh 3vh #382603 inset, 0vh 0vh 1.7vh #c17717;
  background: #9c6614;
  border: 0.1vh solid transparent;
}

.main__clothing-input-item-input input {
  font-family: var(--ff-body);
  font-size: 1.5vh;
  color: #ffffff;
  width: 100%;
  text-align: center;
  outline: none;
  border: none;
  background: none;
  font-weight: 200;
  width: 20vh;
  height: 3vh;
  padding: 0.5vh;
  border-bottom: 0.1vh solid rgba(255, 255, 255, 0.05);
}

.main__clothing-input-item-wrapper-header {
  color: #fff;
  padding-bottom: 1vh;
}

.main__clothing-input-item-wrapper-header p {
  /* font-family: var(--ff-arame); */
  font-size: 1.5vh;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.5);
}

.main__clothing-input-item-wrapper-header p span {
  font-weight: 500;
  /* font-family: var(--ff-arame); */
  font-size: 1.5vh;
  color: #fff;
}

.main__clothing-input-item-wrapper-header-stripe {
  position: relative;
  width: 100%;
  margin-top: 0.1vh;
  height: 0.1vh;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
}

.main__clothing-input-item-wrapper-header-stripe-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 10vh;
  height: 0.1vh;
  background: #fd7403;
  box-shadow: 0vh 0vh 1.7vh #fd7403;
}

.main__clothing-price-container {
  margin-top: 1vh;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 0.5fr;
  gap: 1vh;
}

.main__clothing-price-container-left {
  width: 100%;
  height: 5vh;
  padding: 0.5vh;
  padding-left: 1vh;
  padding-right: 1vh;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  font-size: 1.5vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main__clothing-price-container-left p:first-child {
  color: #fff;
}

.main__clothing-price-container-left p:last-child {
  color: #fd9103;
  text-shadow: 0vh 0vh 1.7vh #fd9103;
}

.main__clothing-price-container-right-btn-1 {
  position: relative;
  width: 5vh;
  height: 5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-price-container-right-btn-1:hover {
  box-shadow: 0vh 0vh 1.7vh #ffa75e inset;
}

.main__clothing-price-container-right-btn-1 img {
  width: 70%;
}

.main__clothing-price-container-right-btn-2 {
  position: relative;
  width: 5vh;
  height: 5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-price-container-right-btn-3 {
  position: relative;
  width: 5vh;
  height: 5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-price-container-right-btn-2:hover {
  box-shadow: 0vh 0vh 1.7vh #ffa927 inset;
}

.main__clothing-price-container-right-btn-2 img,
.main__clothing-price-container-right-btn-3 img {
  width: 70%;
}

.main__clothing-price-container-right {
  display: flex;
  align-items: center;
  gap: 1vh;
}

.main__clothing-price-container-right-btn-3:hover {
  box-shadow: 0vh 0vh 1.7vh #fd9103 inset;
}

.main__clothing-price-container-right-btn-1-stripe {
  position: absolute;
  bottom: -0.1vh;
  left: 50%;
  transform: translateX(-50%);
  height: 0.2vh;
  width: 3vh;
  background: #ffa75e;
  box-shadow: 0vh 0vh 1.7vh #ffa75e;
}

.main__clothing-price-container-right-btn-4-stripe {
  position: absolute;
  bottom: -0.1vh;
  left: 50%;
  transform: translateX(-50%);
  height: 0.2vh;
  width: 3vh;
  background: #ffa600;
  box-shadow: 0vh 0vh 1.7vh #ffa600;
}

.main__clothing-price-container-right-btn-2-stripe {
  position: absolute;
  bottom: -0.1vh;
  left: 50%;
  transform: translateX(-50%);
  height: 0.2vh;
  width: 3vh;
  background: #ffa927;
  box-shadow: 0vh 0vh 1.7vh #ffa927;
}

.main__clothing-price-container-right-btn-3-stripe {
  position: absolute;
  bottom: -0.1vh;
  left: 50%;
  transform: translateX(-50%);
  height: 0.2vh;
  width: 3vh;
  background: #fd9103;
  box-shadow: 0vh 0vh 1.7vh #fd9103;
}

.main__clothing-outfit-container,
.main__clothing-safe-outfit-container {
  width: 100%;
  height: 80vh;
  padding-right: 1vh;
  overflow-y: scroll;
}

.main__clothing-outfit-container::-webkit-scrollbar,
.main__clothing-safe-outfit-container::-webkit-scrollbar {
  width: 0.3vh;
  border-radius: 5vh;
}

.main__clothing-outfit-container::-webkit-scrollbar-track,
.main__clothing-safe-outfit-container::-webkit-scrollbar-track {
  width: 0.3vh;
  border-radius: 5vh;
  background: rgba(255, 255, 255, 0.05);
}

.main__clothing-outfit-container::-webkit-scrollbar-thumb,
.main__clothing-safe-outfit-container::-webkit-scrollbar-thumb {
  width: 0.3vh;
  border-radius: 5vh;
  background: #fd7403;
}

.main__clothing-outfit-item {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 0.025fr;
  gap: 0.5vh;
  margin-top: 1vh;
  transition: 0.2s ease-in;
}

.main__clothing-outfit-item:first-child {
  margin-top: 0vh;
}

.main__clothing-outfit-item-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5vh;
  width: 100%;
  padding: 0.5vh;
  border-radius: 0.5vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
}

.main__corleone-outfit-item-right {
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__corleone-outfit-item-right-2 {
  border: 0.1vh solid #ffbd30;
  background: linear-gradient(0deg, #ff9d09 0%, rgba(184, 129, 28, 0) 100%);
  box-shadow: 0vh 0vh 3.7vh 0vh #eeba4b inset,
    0vh 0.4vh 3vh 0vh rgba(184, 129, 28, 0.55);
  padding: 0.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5vh;
  color: #fff;
  border-radius: 0.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__corleone-outfit-item-right-2:hover {
  box-shadow: 0vh 0vh 1.7vh #ffa927 inset;
}

.main__corleone-outfit-item-right i {
  font-size: 1.5vh;
  color: rgba(255, 255, 255, 0.5);
  transition: 0.2s ease-in;
}

.main__corleone-outfit-item-right:hover {
  box-shadow: 0vh 0vh 1.7vh #fd7403 inset;
  color: #fff !important;
}

.main__corleone-outfit-item-right:hover i {
  color: #fff;
}

.main__clothing-outfit-item-icon {
  width: 4vh;
  height: 4vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main__clothing-outfit-item-icon img {
  width: 100%;
}

.main__clothing-outfit-item-name {
  width: 12.5vh;
  font-size: 1.5vh;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-outfit-item-name:hover {
  opacity: 0.5;
}

.main__clothing-outfit-item-name-2 {
  width: 19vh;
  font-size: 1.5vh;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-outfit-item-name-2:hover {
  opacity: 0.5;
}

.main__clothing-outfit-item-name P:first-child {
  font-size: 1vh;
}

.outfit__name {
  font-size: 1vh;
}

.main__clothing-outfit-item-name p:last-child {
  font-size: 1.5vh;
  color: #fd7403;
  text-shadow: 0vh 0vh 1.7vh #fd7403;
}

.outfit__name-2 {
  font-size: 1.5vh;
  color: #fd7403;
  text-shadow: 0vh 0vh 1.7vh #fd7403;
}

.main__clothing-outfit-btn-container {
  display: flex;
  align-items: center;
  gap: 0.5vh;
}

.main__clothing-outfit-btn-1 {
  width: 2.5vh;
  height: 2.5vh;
  border-radius: 0.25vh;
  cursor: pointer;
  transition: 0.2s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.25vh;
  border: 0.1vh solid #fd9103;
  background: linear-gradient(0deg, #684329 0%, rgba(184, 129, 28, 0) 100%);
  box-shadow: 0vh 0vh 3.7vh 0vh #fd9103 inset,
    0vh 0.4vh 3vh 0vh rgba(184, 98, 28, 0.55);
  color: #fff;
}

.main__clothing-outfit-btn-1:hover {
  box-shadow: 0vh 0vh 1.7vh #fd9103 inset;
}

.main__clothing-outfit-btn-2 {
  width: 2.5vh;
  height: 2.5vh;
  border-radius: 0.25vh;
  cursor: pointer;
  transition: 0.2s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5vh;
  border: 0.1vh solid #ffbd30;
  background: linear-gradient(0deg, #ff9d09 0%, rgba(184, 129, 28, 0) 100%);
  box-shadow: 0vh 0vh 3.7vh 0vh #eeba4b inset,
    0vh 0.4vh 3vh 0vh rgba(184, 129, 28, 0.55);
  color: #fff;
}

.main__clothing-outfit-btn-1 i {
  margin-top: 0.25vh;
}

.main__clothing-outfit-btn-2:hover {
  box-shadow: 0vh 0vh 1.7vh #ffbd30 inset;
}

.main__clothing-grid-item-right-popup {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  background: url(images/popup_bg.png);
  background-size: cover;
  background-position: center;
  width: 30vh;
  padding: 2vh;
  border-radius: 1vh;
}

.main__clothing-grid-item-right-popup-header {
  /* font-family: var(--ff-arame); */
  font-size: 2vh;
  font-weight: 500;
  color: #fff;
  text-align: center;
  width: 100%;
}

.main__clothing-grid-item-right-popup-header p:first-child {
  font-size: 1.5vh;
  color: rgba(255, 255, 255, 0.5);
}

.main__clothing-grid-item-right-popup-text {
  margin-top: 0.5vh;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.5vh;
  width: 100%;
  text-align: center;
}

.main__clothing-grid-item-right-popup-text p span {
  color: #fd7403;
  text-shadow: 0vh 0vh 1.7vh #fd7403;
}

.main__clothing-grid-item-right-popup-input input {
  width: 25.5vh;
  padding: 1vh;
  background: radial-gradient(
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.05)
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5vh;
  margin-top: 1vh;
  text-align: center;
  font-family: var(--ff-gilroy);
  font-size: 1.5vh;
  color: #fff;
}

.main__clothing-grid-item-right-popup-input input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.main__clothing-confirm-btn-grid {
  width: 25.5vh;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1vh;
  margin-top: 1vh;
}

.main__clothing-confirm-btn-1 {
  width: 100%;
  padding: 0.75vh;
  font-size: 1.5vh;
  color: #fff;
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
  border-radius: 0.25vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-confirm-btn-1:hover {
  box-shadow: 0vh 0vh 3vh #382603 inset, 0vh 0vh 1.7vh #c17717;
  background: #9c6614;
  border: 0.1vh solid transparent;
}

.main__clothing-confirm-btn-2 {
  width: 100%;
  padding: 0.75vh;
  font-size: 1.5vh;
  color: #fff;
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.15) inset;
  border-radius: 0.25vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-confirm-btn-2:hover {
  box-shadow: 0vh 0vh 3vh #795915 inset, 0vh 0vh 1.7vh #fd8a1e;
  background: #926832;
  border: 0.1vh solid transparent;
}

.main__clothing-sphere {
  position: absolute;
  bottom: -10vh;
  left: 50%;
  transform: translateX(-50%);
  width: 35vh;
  height: 15vh;
  background: #ffa600;
  border-radius: 50%;
  filter: blur(12.5vh);
  z-index: -1;
}

.main__clothing-outfit-item-share-container {
  display: flex;
  align-items: center;
  gap: 0.25vh;
  margin-top: 0.25vh;
}

.main__coroelone-clothing-outfit-item-share-owner {
  font-size: 1vh;
  color: #fff;
  border: 0.1vh solid #fd7403;
  background: linear-gradient(0deg, #ffa600 0%, rgba(184, 129, 28, 0) 100%);
  box-shadow: 0vh 0vh 3.7vh 0vh #fd7403 inset,
    0vh 0.4vh 3vh 0vh rgba(28, 96, 184, 0.55);
  padding-left: 0.5vh;
  padding-right: 0.5vh;
  border-radius: 0.25vh;
}

.main__clothing-outfit-item-share-name {
  font-size: 1vh;
  color: #fff;
  padding-left: 0.5vh;
  padding-right: 0.5vh;
  border-radius: 0.25vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
}

.main__clothing-handsup-btn {
  margin-top: 1vh;
  padding: 2vh;
  height: 5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  box-shadow: 0vh 0vh 1.7vh rgba(255, 255, 255, 0.05) inset;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5vh;
  color: #fff;
  border-radius: 0.5vh;
  cursor: pointer;
  gap: 1vh;
  transition: 0.2s ease-in;
}

.main__clothing-handsup-btn:hover {
  box-shadow: 0vh 0vh 3vh #382603 inset, 0vh 0vh 1.7vh #c17717;
  background: #9c6614;
  border: 0.1vh solid transparent;
}

.absolute {
  position: absolute;
  bottom: 8vh;
  left: 2vh;
}

.main__clothing-scroll-item {
  width: 100%;
  height: 12vh;
  background: radial-gradient(
    120.83% 120.83% at 50% 0%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 0.5vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
  transition: 0.2s ease-in;
}

.main__clothing-scroll-item:hover {
  background-size: 150%;
  background-position: center;
}

.main__clothing-scroll-item-name {
  font-family: var(--ff-bebas);
  font-size: 2vh;
  color: #fff;
  margin-top: 1vh;
}

.main__clothing-scroll-item-img {
  width: 7.5vh;
  height: 7vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main__clothing-scroll-item-img img {
  width: 100%;
}
