<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>final_clothingshop</title>
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/sharp-regular.css" />
        <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.6.0/css/all.css" />
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <div class="main__clothing-shop-container">
            <div class="main__clothing-bg"></div>
            <div class="main__clothing-container">
                <div class="main__fasocity-header-container">
                    <div style="margin-left: 7vh; display: flex; align-items: center; gap: 1vh">
                        <div class="main__fasocity-header-left-stripe"></div>
                        <div class="main__fasocity-header-left">
                            <p>FINAL</p>
                            <p>Clothingshop</p>
                        </div>
                    </div>
                    <div class="main__fasocity-header-container-right">
                        <div class="main__fasocity-header-container-right-close-container">
                            <i class="fa-sharp fa-regular fa-xmark" aria-hidden="true"></i>
                        </div>
                    </div>
                </div>
                <div class="main__fasocity-header-bg">
                    <img src="images/torso.png" alt="">
                </div>
    
                <div class="main__clothing-category-btn-container">
                    <div id="clothingCategory" class="main__clothing-category-btn active">
                        <p>Kleidung</p>
                        <!-- <div class="main__clothing-category-btn-stripe"></div> -->
                    </div>
                    <div id="outfitsCategory" class="main__clothing-category-btn">
                        <p>Outfits</p>
                        <!-- <div class="main__clothing-category-btn-stripe"></div> -->
                    </div>
                    <div id="sharedCategory" class="main__clothing-category-btn">
                        <p>Geteilte</p>
                        <!-- <div class="main__clothing-category-btn-stripe"></div> -->
                    </div>
                </div>
    
                <div class="main__clothing-opacity-container" style="display: none;">
                    <div class="main__clothing-scroll-container">
                        <!-- clothing parts appended here -->
                    </div>
    
                    <div class="main__clothing-input-item-wrapper">
                        <div class="main__clothing-input-item-wrapper-header">
                            <p>Kleidungs <span>Texture</span></p>
                            <div class="main__clothing-input-item-wrapper-header-stripe"></div>
                        </div>
                        <div class="main__clothing-input-item">
                            <div id="drawableLeft" class="main__clothing-input-item-btn">
                                <i class="fa-regular fa-chevron-left" aria-hidden="true"></i>
                            </div>
                            <div class="main__clothing-input-item-input">
                                <input id="drawableValue" type="text" placeholder="123">
                            </div>
                            <div id="drawableRight" class="main__clothing-input-item-btn">
                                <i class="fa-regular fa-chevron-right" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
    
                    <div class="main__clothing-input-item-wrapper">
                        <div class="main__clothing-input-item-wrapper-header">
                            <p>Kleidungs <span>Style</span></p>
                            <div class="main__clothing-input-item-wrapper-header-stripe"></div>
                        </div>
                        <div class="main__clothing-input-item">
                            <div id="textureLeft" class="main__clothing-input-item-btn">
                                <i class="fa-regular fa-chevron-left" aria-hidden="true"></i>
                            </div>
                            <div class="main__clothing-input-item-input">
                                <input id="textureValue" type="text" placeholder="123">
                            </div>
                            <div id="textureRight" class="main__clothing-input-item-btn">
                                <i class="fa-regular fa-chevron-right" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
    
                    <div class="main__clothing-price-container">
                        <div class="main__clothing-price-container-left">
                            <p>Total:</p>
                            <p id="clothprice">0 €</p>
                        </div>
                        <div class="main__clothing-price-container-right">
                            <div id="cashPayment" class="main__clothing-price-container-right-btn-1">
                                <img src="images/money.png" alt="">
                                <div class="main__clothing-price-container-right-btn-1-stripe"></div>
                            </div>
                            <div id="cardPayment" class="main__clothing-price-container-right-btn-2">
                                <img src="images/card.png" alt="">
                                <div class="main__clothing-price-container-right-btn-2-stripe"></div>
                            </div>
                        </div>
                    </div>
    
                    <div id="handsUpButton" class="main__clothing-handsup-btn">
                        <i class="fa-solid fa-person" aria-hidden="true"></i>
                        <!-- <div class="main__clothing-price-container-right-btn-4-stripe"></div> -->
                        <p>Hände hoch</p>
                    </div>
                </div>
    
                <div class="main__clothing-grid-item-right-popup" style="display: none;">
                    <div class="main__clothing-grid-item-right-popup-header">
                        <p>FINAL CITY</p>
                        <p>Outfit Speichern?</p>
                    </div>
                    <div class="main__clothing-grid-item-right-popup-text">
                        <p>Wenn du dein Outfit speichern möchtest,<br> gib dein Outfit Namen an!</p>
                    </div>
                    <div class="main__clothing-grid-item-right-popup-input">
                        <input id="outfitNameInput" type="text" placeholder="Outfit Name">
                    </div>
                    <div class="main__clothing-confirm-btn-grid">
                        <div id="saveOutfit" class="main__clothing-confirm-btn-1">
                            <p>Ja</p>
                        </div>
                        <div id="cancelOutfit" class="main__clothing-confirm-btn-2">
                            <p>Nein</p>
                        </div>
                    </div>
                </div>
    
                <div class="main__clothing-outfit-gird-container" style="display: none;">
                    <div class="main__clothing-category-search">
                        <i class="fa-regular fa-magnifying-glass" aria-hidden="true"></i>
                        <input id="searchOutfitsInput" type="text" placeholder="Outfit suchen">
                    </div>
    
                    <div class="main__clothing-safe-outfit-container">
                        <!-- user outfits saved here -->
                    </div>
                </div>
    
                <div class="sharedOutfitsHere" style="display: none;">
                    <div class="main__clothing-category-search">
                        <i class="fa-regular fa-plus" aria-hidden="true"></i>
                        <input id="addOutfitInput" type="text" placeholder="Add outfit">
                    </div>
                    <div class="main__clothing-outfit-container">
                        <!-- shared outfits here -->
                    </div>
                </div>
            </div>
        </div>

        <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
        <script src="script.js"></script>
    </body>
</html>
